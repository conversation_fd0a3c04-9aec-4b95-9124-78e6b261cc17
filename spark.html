<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Apache Spark: Behind the Scenes of Big Data Processing</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css" rel="stylesheet" />
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        h1 {
            color: #e25a1c;
            text-align: center;
            border-bottom: 3px solid #e25a1c;
            padding-bottom: 10px;
        }
        h2 {
            color: #2c3e50;
            border-left: 4px solid #e25a1c;
            padding-left: 15px;
            margin-top: 30px;
        }
        h3 {
            color: #34495e;
            margin-top: 25px;
        }
        .architecture-diagram {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            text-align: center;
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
        }
        .highlight {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .process-flow {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .process-step {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 5px;
            flex: 1;
            min-width: 200px;
            text-align: center;
        }
        .arrow {
            font-size: 24px;
            color: #e25a1c;
            margin: 0 10px;
        }
        ul, ol {
            padding-left: 25px;
        }
        li {
            margin: 8px 0;
        }
        .performance-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .performance-table th, .performance-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .performance-table th {
            background-color: #e25a1c;
            color: white;
        }
        .performance-table tr:nth-child(even) {
            background-color: #f2f2f2;
        }

        /* Interactive Elements Styles */
        .interactive-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .interactive-demo {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }

        .clickable-component {
            background: #3498db;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            border: none;
            min-width: 150px;
        }

        .clickable-component:hover {
            background: #2980b9;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }

        .clickable-component.active {
            background: #e74c3c;
            transform: scale(1.05);
            box-shadow: 0 0 15px rgba(231, 76, 60, 0.5);
        }

        .info-panel {
            background: white;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            margin: 10px 0;
            min-height: 100px;
            display: none;
        }

        .info-panel.active {
            display: block;
            animation: fadeIn 0.3s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .code-editor {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            min-height: 200px;
            border: none;
            width: 100%;
            resize: vertical;
        }

        .run-button {
            background: #27ae60;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 0;
            font-weight: bold;
        }

        .run-button:hover {
            background: #229954;
        }

        .output-panel {
            background: #1a1a1a;
            color: #00ff00;
            padding: 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            min-height: 100px;
            margin: 10px 0;
            white-space: pre-wrap;
        }

        .memory-visualizer {
            display: flex;
            height: 200px;
            border: 2px solid #333;
            border-radius: 5px;
            overflow: hidden;
            margin: 20px 0;
        }

        .memory-segment {
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .execution-memory {
            background: #e74c3c;
        }

        .storage-memory {
            background: #3498db;
        }

        .user-memory {
            background: #f39c12;
        }

        .quiz-container {
            background: #ecf0f1;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }

        .quiz-question {
            font-weight: bold;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .quiz-option {
            background: white;
            border: 2px solid #bdc3c7;
            padding: 10px;
            margin: 5px 0;
            border-radius: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .quiz-option:hover {
            border-color: #3498db;
            background: #ebf3fd;
        }

        .quiz-option.correct {
            border-color: #27ae60;
            background: #d5f4e6;
        }

        .quiz-option.incorrect {
            border-color: #e74c3c;
            background: #fadbd8;
        }

        .performance-chart {
            max-width: 600px;
            margin: 20px auto;
        }

        .slider-container {
            margin: 20px 0;
        }

        .slider {
            width: 100%;
            margin: 10px 0;
        }

        .slider-label {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        .dag-node {
            background: #3498db;
            color: white;
            padding: 10px;
            border-radius: 5px;
            margin: 5px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            min-width: 100px;
        }

        .dag-node:hover {
            background: #2980b9;
            transform: scale(1.05);
        }

        .dag-node.executed {
            background: #27ae60;
        }

        .dag-arrow {
            font-size: 24px;
            color: #e25a1c;
            margin: 0 10px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 0.5; }
            50% { opacity: 1; }
            100% { opacity: 0.5; }
        }

        .tab-container {
            margin: 20px 0;
        }

        .tab-buttons {
            display: flex;
            border-bottom: 2px solid #ddd;
        }

        .tab-button {
            background: #f8f9fa;
            border: none;
            padding: 10px 20px;
            cursor: pointer;
            border-top: 2px solid transparent;
            transition: all 0.3s ease;
        }

        .tab-button.active {
            background: white;
            border-top-color: #e25a1c;
            color: #e25a1c;
        }

        .tab-content {
            display: none;
            padding: 20px;
            background: white;
            border: 1px solid #ddd;
            border-top: none;
        }

        .tab-content.active {
            display: block;
        }

        .fact-check {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
        }

        .fact-check::before {
            content: "✓ Fact-Checked: ";
            font-weight: bold;
            color: #28a745;
        }

        /* Enhanced Interactive Architecture Styles */
        .enhanced-architecture-container {
            background: linear-gradient(135deg, #ff6b35 0%, #e25a1c 100%);
            border-radius: 20px;
            padding: 30px;
            margin: 30px 0;
            position: relative;
            min-height: 480px;
            overflow: hidden;
        }

        .spark-component {
            position: absolute;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 12px;
            padding: 12px 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.1);
            text-align: center;
            width: 120px;
            height: 100px;
            border: 2px solid transparent;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .spark-component:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
            border-color: #ffb300;
        }

        .spark-component.active {
            border-color: #ff6b35;
            background: linear-gradient(135deg, #fff 0%, #f8f9fa 100%);
            transform: scale(1.05);
        }

        .component-icon {
            font-size: 1.8em;
            margin-bottom: 4px;
            display: block;
            line-height: 1;
        }

        .component-title {
            font-weight: bold;
            color: #e25a1c;
            margin-bottom: 2px;
            font-size: 0.75em;
            line-height: 1.1;
        }

        .component-subtitle {
            font-size: 0.6em;
            color: #666;
            opacity: 0.8;
            line-height: 1;
        }

        /* Connection Lines - Execution Flow Based */
        .connection-line {
            position: absolute;
            background: linear-gradient(90deg, #ffb300, #ff6b35);
            height: 3px;
            border-radius: 2px;
            opacity: 0.4;
            transition: all 0.3s ease;
            z-index: 2;
            pointer-events: none;
        }

        .connection-line.active {
            opacity: 1;
            height: 4px;
            box-shadow: 0 0 10px rgba(255, 179, 0, 0.5);
            z-index: 3;
        }

        /* Execution Step Colors with Layering */
        .execution-step-1 { background: linear-gradient(90deg, #3498db, #2980b9); z-index: 10; } /* Resource Request - Blue */
        .execution-step-2 { background: linear-gradient(90deg, #9b59b6, #8e44ad); z-index: 11; } /* Negotiation - Purple */
        .execution-step-3 { background: linear-gradient(90deg, #e74c3c, #c0392b); z-index: 12; } /* DAG Creation - Red */
        .execution-step-4 { background: linear-gradient(90deg, #f39c12, #e67e22); z-index: 13; } /* Executor Launch - Orange */
        .execution-step-5 { background: linear-gradient(90deg, #27ae60, #229954); z-index: 14; } /* Task Distribution - Green */
        .execution-step-6 { background: linear-gradient(90deg, #16a085, #138d75); z-index: 15; } /* Data Operations - Teal */
        .execution-step-7 { background: linear-gradient(90deg, #2c3e50, #34495e); z-index: 16; } /* Results Collection - Dark */

        /* Step Animation */
        .execution-step-active {
            opacity: 1 !important;
            height: 5px !important;
            animation: stepPulse 2s ease-in-out infinite;
        }

        @keyframes stepPulse {
            0%, 100% {
                box-shadow: 0 0 5px rgba(255, 179, 0, 0.3);
                transform: scale(1);
            }
            50% {
                box-shadow: 0 0 15px rgba(255, 179, 0, 0.8);
                transform: scale(1.02);
            }
        }

        /* Execution Flow Legend */
        .execution-legend {
            position: absolute;
            bottom: 20px;
            left: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 10px;
            padding: 15px;
            font-size: 0.8em;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
            max-width: 200px;
        }

        .legend-item {
            display: flex;
            align-items: center;
            margin-bottom: 5px;
        }

        .legend-color {
            width: 20px;
            height: 3px;
            border-radius: 2px;
            margin-right: 8px;
        }

        .legend-step-1 { background: linear-gradient(90deg, #3498db, #2980b9); }
        .legend-step-2 { background: linear-gradient(90deg, #9b59b6, #8e44ad); }
        .legend-step-3 { background: linear-gradient(90deg, #e74c3c, #c0392b); }
        .legend-step-4 { background: linear-gradient(90deg, #f39c12, #e67e22); }
        .legend-step-5 { background: linear-gradient(90deg, #27ae60, #229954); }
        .legend-step-6 { background: linear-gradient(90deg, #16a085, #138d75); }
        .legend-step-7 { background: linear-gradient(90deg, #2c3e50, #34495e); }

        /* Data Flow Animation */
        .data-packet {
            position: absolute;
            width: 12px;
            height: 12px;
            background: radial-gradient(circle, #ffb300 0%, #ff6b35 100%);
            border-radius: 50%;
            box-shadow: 0 0 10px rgba(255, 179, 0, 0.8);
            animation: sparkDataFlow 3s linear infinite;
            z-index: 10;
        }

        @keyframes sparkDataFlow {
            0% { opacity: 0; transform: scale(0.5); }
            10% { opacity: 1; transform: scale(1); }
            90% { opacity: 1; transform: scale(1); }
            100% { opacity: 0; transform: scale(0.5); }
        }

        /* Enhanced Statistics Panel */
        .spark-stats-panel {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.95);
            border-radius: 15px;
            padding: 15px;
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
            width: 180px;
            backdrop-filter: blur(10px);
            font-size: 0.85em;
        }

        .spark-stats-panel h4 {
            margin-bottom: 15px;
            color: #e25a1c;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .stat-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 8px;
            font-size: 0.9em;
            color: #666;
        }

        .stat-value {
            font-weight: bold;
            color: #e25a1c;
        }

        /* Enhanced Info Panel */
        .enhanced-spark-info-panel {
            background: linear-gradient(135deg, #ff6b35 0%, #e25a1c 100%);
            color: white;
            border-radius: 15px;
            padding: 25px;
            margin-top: 20px;
            min-height: 200px;
            transition: all 0.3s ease;
        }

        .enhanced-spark-info-panel h3 {
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .info-content {
            line-height: 1.6;
        }

        .info-specs {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 15px;
            margin-top: 15px;
        }

        .info-specs h4 {
            margin-bottom: 10px;
            color: #ffb300;
        }

        /* Header Enhancement */
        .spark-header {
            text-align: center;
            background: linear-gradient(135deg, #e09f88 0%, #f8f8f8 100%);
            color: white;
            padding: 60px 30px;
            border-radius: 20px;
            margin-bottom: 40px;
            box-shadow: 0 10px 30px rgba(226, 90, 28, 0.3);
            position: relative;
            overflow: hidden;
        }

        .spark-header::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: radial-gradient(circle, rgba(255, 255, 255, 0.1) 0%, transparent 70%);
            animation: sparkHeaderGlow 6s ease-in-out infinite alternate;
        }

        @keyframes sparkHeaderGlow {
            0% { transform: rotate(0deg) scale(1); opacity: 0.3; }
            100% { transform: rotate(180deg) scale(1.1); opacity: 0.1; }
        }

        .spark-header h1 {
            font-size: 2.8em;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
            position: relative;
            z-index: 2;
        }

        .spark-header p {
            font-size: 1.2em;
            opacity: 0.95;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
            position: relative;
            z-index: 2;
        }

        .spark-header-stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-top: 30px;
            flex-wrap: wrap;
            position: relative;
            z-index: 2;
        }

        .spark-header-stat {
            background: rgba(255, 255, 255, 0.15);
            padding: 15px 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;
        }

        .spark-header-stat:hover {
            background: rgba(255, 255, 255, 0.25);
            transform: translateY(-2px);
        }

        .spark-header-stat-number {
            font-size: 1.8em;
            font-weight: bold;
            display: block;
            color: #ffb300;
        }

        .spark-header-stat-label {
            font-size: 0.9em;
            opacity: 0.9;
            margin-top: 5px;
        }

        /* Responsive design for Spark architecture */
        @media (max-width: 768px) {
            .enhanced-architecture-container {
                min-height: auto;
                padding: 20px;
            }

            .spark-component {
                position: relative !important;
                margin: 15px auto;
                display: block;
                width: 140px;
                height: 110px;
                left: auto !important;
                top: auto !important;
                right: auto !important;
                bottom: auto !important;
            }

            .spark-stats-panel {
                position: relative !important;
                top: auto !important;
                right: auto !important;
                margin: 20px auto;
                width: 100%;
                max-width: 300px;
            }

            .connection-line {
                display: none;
            }

            .component-title {
                font-size: 0.8em;
            }

            .component-subtitle {
                font-size: 0.65em;
            }
        }

        @media (max-width: 600px) {
            .enhanced-architecture-container {
                padding: 15px;
            }

            .spark-component {
                width: 120px;
                height: 100px;
                margin: 10px auto;
            }

            .component-icon {
                font-size: 1.6em;
            }

            .component-title {
                font-size: 0.7em;
            }

            .component-subtitle {
                font-size: 0.6em;
            }
        }
    </style>
</head>
<body>
    <nav style="background: rgba(0,0,0,0.1); padding: 10px 0; text-align: center;">
        <a href="index.html" style="color: #667eea; text-decoration: none; margin: 0 15px; font-weight: bold;">🏠 Big Data Hub</a>
        <a href="hadoop.html" style="color: #ff6b35; text-decoration: none; margin: 0 15px; font-weight: bold;">🔧 Hadoop</a>
        <a href="hive.html" style="color: #ffb300; text-decoration: none; margin: 0 15px; font-weight: bold;">🐝 Hive</a>
        <a href="spark.html" style="color: #e25a1c; text-decoration: none; margin: 0 15px; font-weight: bold;">⚡ Spark</a>
    </nav>
    <div class="container">
        <div class="spark-header">
            <h1>🚀 Interactive Apache Spark: Behind the Scenes of Big Data Processing</h1>
            <p>
                Comprehensive guide to Apache Spark's unified analytics engine for large-scale data processing.<br />
                Understanding in-memory computing, RDDs, and distributed processing architecture.
            </p>
            <div class="spark-header-stats">
                <div class="spark-header-stat">
                    <span class="spark-header-stat-number">100x</span>
                    <div class="spark-header-stat-label">Faster In-Memory</div>
                </div>
                <div class="spark-header-stat">
                    <span class="spark-header-stat-number">4</span>
                    <div class="spark-header-stat-label">Core Components</div>
                </div>
                <div class="spark-header-stat">
                    <span class="spark-header-stat-number">5</span>
                    <div class="spark-header-stat-label">Programming Languages</div>
                </div>
                <div class="spark-header-stat">
                    <span class="spark-header-stat-number">∞</span>
                    <div class="spark-header-stat-label">Scalability</div>
                </div>
            </div>
        </div>

        <div class="interactive-section">
            <h2>🎯 Welcome to Interactive Spark Learning!</h2>
            <p>Explore Apache Spark's architecture through hands-on demonstrations, interactive visualizations, and fact-checked content from official Apache Spark documentation.</p>
            <div style="text-align: center; margin: 20px 0;">
                <button class="clickable-component" onclick="startInteractiveTour()">🎮 Start Interactive Tour</button>
                <button class="clickable-component" onclick="showQuickFacts()">📊 Quick Facts</button>
                <button class="clickable-component" onclick="jumpToPlayground()">🛠️ Code Playground</button>
            </div>
        </div>

        <div id="quick-facts" class="info-panel">
            <h3>⚡ Spark Quick Facts (Verified from Apache Documentation)</h3>
            <ul>
                <li><strong>Speed:</strong> 100x faster than Hadoop MapReduce in memory, 10x faster on disk</li>
                <li><strong>Memory Model:</strong> 60% heap for execution/storage, 40% for user data</li>
                <li><strong>Languages:</strong> Java, Scala, Python, R, SQL</li>
                <li><strong>Components:</strong> Spark Core, SQL, Streaming, MLlib, GraphX</li>
                <li><strong>Fault Tolerance:</strong> Lineage-based recovery without replication</li>
            </ul>
        </div>

        <h2>1. Introduction to Apache Spark</h2>
        <p>Apache Spark is a unified analytics engine for large-scale data processing that provides high-level APIs in Java, Scala, Python, and R. Unlike traditional MapReduce systems, Spark performs computations in memory, making it significantly faster for iterative algorithms and interactive data mining.</p>

        <div class="fact-check">
            This performance claim is verified by official Apache Spark benchmarks showing 100x faster in-memory processing and 10x faster disk-based processing compared to traditional MapReduce systems.
        </div>

        <div class="highlight">
            <strong>Key Innovation:</strong> Spark's Resilient Distributed Datasets (RDDs) enable fault-tolerant, in-memory computing across distributed clusters.
        </div>

        <h2>2. Interactive Spark Architecture</h2>
        <p style="margin-bottom: 20px; color: #666; text-align: center;">
            🎯 <strong>Click on any component</strong> to explore its role in the Spark ecosystem •
            Watch the <strong>real-time data flow</strong> visualization
        </p>

        <!-- Execution Flow Controls -->
        <div style="text-align: center; margin-bottom: 20px;">
            <button onclick="demonstrateExecutionFlow()" style="background: linear-gradient(135deg, #e74c3c, #c0392b); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-weight: 600; margin: 0 10px; transition: all 0.3s ease;">
                🎬 Show Complete Execution Flow
            </button>
            <button onclick="resetExecutionFlow()" style="background: linear-gradient(135deg, #95a5a6, #7f8c8d); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-weight: 600; margin: 0 10px; transition: all 0.3s ease;">
                🔄 Reset Flow
            </button>
        </div>

        <!-- Enhanced Interactive Architecture Container -->
        <div class="enhanced-architecture-container" id="spark-architecture">
            <!-- Driver Program -->
            <div class="spark-component" id="driver-component"
                 style="top: 40px; left: 40px;"
                 onclick="showSparkComponentDetails('driver')">
                <span class="component-icon">🚗</span>
                <div class="component-title">Driver Program</div>
                <div class="component-subtitle">SparkContext</div>
            </div>

            <!-- Cluster Manager -->
            <div class="spark-component" id="cluster-manager-component"
                 style="top: 40px; left: 200px;"
                 onclick="showSparkComponentDetails('cluster-manager')">
                <span class="component-icon">🎯</span>
                <div class="component-title">Cluster Manager</div>
                <div class="component-subtitle">Resources</div>
            </div>

            <!-- Application Master -->
            <div class="spark-component" id="app-master-component"
                 style="top: 40px; left: 360px;"
                 onclick="showSparkComponentDetails('app-master')">
                <span class="component-icon">👑</span>
                <div class="component-title">App Master</div>
                <div class="component-subtitle">Coordination</div>
            </div>

            <!-- Worker Node 1 -->
            <div class="spark-component" id="worker1-component"
                 style="top: 180px; left: 40px;"
                 onclick="showSparkComponentDetails('worker')">
                <span class="component-icon">⚙️</span>
                <div class="component-title">Worker Node 1</div>
                <div class="component-subtitle">Executors</div>
            </div>

            <!-- Worker Node 2 -->
            <div class="spark-component" id="worker2-component"
                 style="top: 180px; left: 200px;"
                 onclick="showSparkComponentDetails('worker')">
                <span class="component-icon">⚙️</span>
                <div class="component-title">Worker Node 2</div>
                <div class="component-subtitle">Executors</div>
            </div>

            <!-- Worker Node 3 -->
            <div class="spark-component" id="worker3-component"
                 style="top: 180px; left: 360px;"
                 onclick="showSparkComponentDetails('worker')">
                <span class="component-icon">⚙️</span>
                <div class="component-title">Worker Node 3</div>
                <div class="component-subtitle">Executors</div>
            </div>

            <!-- RDD Storage -->
            <div class="spark-component" id="rdd-component"
                 style="top: 320px; left: 120px;"
                 onclick="showSparkComponentDetails('rdd')">
                <span class="component-icon">💾</span>
                <div class="component-title">RDD Storage</div>
                <div class="component-subtitle">Memory Cache</div>
            </div>

            <!-- DAG Scheduler -->
            <div class="spark-component" id="dag-component"
                 style="top: 320px; left: 280px;"
                 onclick="showSparkComponentDetails('dag')">
                <span class="component-icon">📊</span>
                <div class="component-title">DAG Scheduler</div>
                <div class="component-subtitle">Task Planning</div>
            </div>

            <!-- Connection Lines - Following Spark Execution Flow (Non-overlapping) -->

            <!-- Step 1: Driver requests resources from Cluster Manager -->
            <div class="connection-line execution-step-1" id="driver-to-cluster"
                 style="top: 85px; left: 160px; width: 40px;"
                 data-step="1" data-description="Driver requests resources"></div>

            <!-- Step 2: Cluster Manager negotiates with Application Master -->
            <div class="connection-line execution-step-2" id="cluster-to-appmaster"
                 style="top: 85px; left: 320px; width: 40px;"
                 data-step="2" data-description="Resource negotiation"></div>

            <!-- Step 3: Driver creates DAG and sends to DAG Scheduler -->
            <div class="connection-line execution-step-3" id="driver-to-dag"
                 style="top: 150px; left: 100px; width: 220px; height: 3px; transform: rotate(35deg); transform-origin: left center;"
                 data-step="3" data-description="DAG creation and scheduling"></div>

            <!-- Step 4: Application Master launches Workers (separate paths to avoid overlap) -->
            <div class="connection-line execution-step-4" id="appmaster-to-worker1"
                 style="top: 140px; left: 350px; width: 120px; height: 3px; transform: rotate(155deg); transform-origin: left center;"
                 data-step="4" data-description="Launch executor 1"></div>
            <div class="connection-line execution-step-4" id="appmaster-to-worker2"
                 style="top: 140px; left: 380px; width: 40px; height: 3px; transform: rotate(90deg); transform-origin: center;"
                 data-step="4" data-description="Launch executor 2"></div>
            <div class="connection-line execution-step-4" id="appmaster-to-worker3"
                 style="top: 140px; left: 420px; width: 40px; height: 3px; transform: rotate(90deg); transform-origin: center;"
                 data-step="4" data-description="Launch executor 3"></div>

            <!-- Step 5: DAG Scheduler sends tasks to Workers (curved paths) -->
            <div class="connection-line execution-step-5" id="dag-to-worker1"
                 style="top: 320px; left: 120px; width: 140px; height: 3px; transform: rotate(-55deg); transform-origin: right center;"
                 data-step="5" data-description="Task to worker 1"></div>
            <div class="connection-line execution-step-5" id="dag-to-worker2"
                 style="top: 320px; left: 220px; width: 60px; height: 3px; transform: rotate(-80deg); transform-origin: right center;"
                 data-step="5" data-description="Task to worker 2"></div>
            <div class="connection-line execution-step-5" id="dag-to-worker3"
                 style="top: 320px; left: 300px; width: 100px; height: 3px; transform: rotate(-35deg); transform-origin: left center;"
                 data-step="5" data-description="Task to worker 3"></div>

            <!-- Step 6: Workers store/retrieve data from RDD Storage (non-overlapping curves) -->
            <div class="connection-line execution-step-6" id="worker1-to-rdd"
                 style="top: 280px; left: 100px; width: 60px; height: 3px; transform: rotate(30deg); transform-origin: left center;"
                 data-step="6" data-description="Data from worker 1"></div>
            <div class="connection-line execution-step-6" id="worker2-to-rdd"
                 style="top: 280px; left: 200px; width: 40px; height: 3px; transform: rotate(90deg); transform-origin: center;"
                 data-step="6" data-description="Data from worker 2"></div>
            <div class="connection-line execution-step-6" id="worker3-to-rdd"
                 style="top: 280px; left: 300px; width: 60px; height: 3px; transform: rotate(150deg); transform-origin: right center;"
                 data-step="6" data-description="Data from worker 3"></div>

            <!-- Step 7: Results flow back to Driver (separate return paths) -->
            <div class="connection-line execution-step-7" id="worker1-to-driver"
                 style="top: 180px; left: 60px; width: 80px; height: 3px; transform: rotate(-25deg); transform-origin: right center;"
                 data-step="7" data-description="Results from worker 1"></div>
            <div class="connection-line execution-step-7" id="worker2-to-driver"
                 style="top: 180px; left: 140px; width: 100px; height: 3px; transform: rotate(-55deg); transform-origin: right center;"
                 data-step="7" data-description="Results from worker 2"></div>
            <div class="connection-line execution-step-7" id="worker3-to-driver"
                 style="top: 180px; left: 240px; width: 140px; height: 3px; transform: rotate(-35deg); transform-origin: right center;"
                 data-step="7" data-description="Results from worker 3"></div>

            <!-- Data Flow Packets -->
            <div id="spark-data-flow-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 5;">
                <!-- Animated data packets will be created here -->
            </div>

            <!-- Real-time Statistics Panel -->
            <div class="spark-stats-panel">
                <h4>⚡ Spark Cluster Stats</h4>
                <div class="stat-item">
                    <span>Active Applications:</span>
                    <span class="stat-value" id="active-apps">3</span>
                </div>
                <div class="stat-item">
                    <span>Running Jobs:</span>
                    <span class="stat-value" id="running-jobs">7</span>
                </div>
                <div class="stat-item">
                    <span>Total Cores:</span>
                    <span class="stat-value" id="total-cores">48</span>
                </div>
                <div class="stat-item">
                    <span>Memory Used:</span>
                    <span class="stat-value" id="memory-used">12.4 GB</span>
                </div>
                <div class="stat-item">
                    <span>RDD Partitions:</span>
                    <span class="stat-value" id="rdd-partitions">156</span>
                </div>
                <div class="stat-item">
                    <span>Cluster Health:</span>
                    <span class="stat-value" style="color: #27ae60;">✓ Healthy</span>
                </div>
            </div>

            <!-- Execution Flow Legend -->
            <div class="execution-legend">
                <h4 style="margin-bottom: 10px; color: #e25a1c; font-size: 0.9em;">🔄 Execution Flow</h4>
                <div class="legend-item">
                    <div class="legend-color legend-step-1"></div>
                    <span>1. Resource Request</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-2"></div>
                    <span>2. Negotiation</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-3"></div>
                    <span>3. DAG Creation</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-4"></div>
                    <span>4. Executor Launch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-5"></div>
                    <span>5. Task Distribution</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-6"></div>
                    <span>6. Data Operations</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color legend-step-7"></div>
                    <span>7. Results Collection</span>
                </div>
            </div>
        </div>

        <!-- Enhanced Component Details Panel -->
        <div class="enhanced-spark-info-panel" id="spark-component-info-panel" style="display: none;">
            <h3 id="spark-component-info-title">Component Details</h3>
            <div class="info-content" id="spark-component-info-content">
                Click on any component above to see detailed information about its role in the Spark architecture.
            </div>
            <div class="info-specs" id="spark-component-info-specs" style="display: none;">
                <h4>Technical Specifications</h4>
                <div id="spark-component-specs-content"></div>
            </div>
        </div>

        <h3>2.1 Core Components</h3>
        <ul>
            <li><strong>Driver Program:</strong> Contains the main() function and creates SparkContext</li>
            <li><strong>SparkContext:</strong> Coordinates the execution of Spark applications</li>
            <li><strong>Cluster Manager:</strong> Allocates resources across applications</li>
            <li><strong>Executors:</strong> Run tasks and store data for the application</li>
            <li><strong>Tasks:</strong> Units of work sent to executors</li>
        </ul>

        <h2>3. Resilient Distributed Datasets (RDDs)</h2>

        <p>RDDs are the fundamental data structure of Spark - immutable, distributed collections of objects that can be processed in parallel.</p>

        <h3>3.1 RDD Characteristics</h3>
        <ul>
            <li><strong>Resilient:</strong> Fault-tolerant through lineage information</li>
            <li><strong>Distributed:</strong> Data is distributed across multiple nodes</li>
            <li><strong>Dataset:</strong> Collection of partitioned data</li>
            <li><strong>Immutable:</strong> Cannot be changed after creation</li>
            <li><strong>Lazy Evaluation:</strong> Transformations are not executed until an action is called</li>
        </ul>

        <div class="interactive-demo" id="rdd-playground">
            <h3>🛠️ Interactive RDD Playground</h3>
            <p>Try editing and running Spark RDD operations below:</p>

            <div class="tab-container">
                <div class="tab-buttons">
                    <button class="tab-button active" onclick="switchTab('creation')">RDD Creation</button>
                    <button class="tab-button" onclick="switchTab('transformations')">Transformations</button>
                    <button class="tab-button" onclick="switchTab('actions')">Actions</button>
                </div>

                <div id="creation" class="tab-content active">
                    <textarea class="code-editor" id="creation-code">
# RDD Creation Example
from pyspark import SparkContext

sc = SparkContext()
# Create RDD from collection
data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
rdd = sc.parallelize(data)
print(f"RDD created with {rdd.count()} elements")

# Create RDD from file (simulated)
text_data = ["Hello Spark", "Big Data Processing", "Distributed Computing"]
text_rdd = sc.parallelize(text_data)
print(f"Text RDD: {text_rdd.collect()}")
                    </textarea>
                    <button class="run-button" onclick="runCode('creation')">▶️ Run Code</button>
                    <div id="creation-output" class="output-panel"></div>
                </div>

                <div id="transformations" class="tab-content">
                    <textarea class="code-editor" id="transformations-code">
# RDD Transformations (Lazy Evaluation)
data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
rdd = sc.parallelize(data)

# Map transformation
squared_rdd = rdd.map(lambda x: x * x)
print("Squared RDD created (lazy)")

# Filter transformation
even_rdd = rdd.filter(lambda x: x % 2 == 0)
print("Even numbers RDD created (lazy)")

# FlatMap transformation
words = ["hello world", "spark is great"]
words_rdd = sc.parallelize(words)
flat_rdd = words_rdd.flatMap(lambda line: line.split())
print("FlatMap RDD created (lazy)")

# No computation happens until an action is called!
                    </textarea>
                    <button class="run-button" onclick="runCode('transformations')">▶️ Run Code</button>
                    <div id="transformations-output" class="output-panel"></div>
                </div>

                <div id="actions" class="tab-content">
                    <textarea class="code-editor" id="actions-code">
# RDD Actions (Trigger Execution)
data = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
rdd = sc.parallelize(data)

# Collect action - brings all data to driver
result = rdd.collect()
print(f"Collect: {result}")

# Count action
count = rdd.count()
print(f"Count: {count}")

# First action
first = rdd.first()
print(f"First element: {first}")

# Take action
first_three = rdd.take(3)
print(f"First 3 elements: {first_three}")

# Reduce action
sum_result = rdd.reduce(lambda a, b: a + b)
print(f"Sum of all elements: {sum_result}")
                    </textarea>
                    <button class="run-button" onclick="runCode('actions')">▶️ Run Code</button>
                    <div id="actions-output" class="output-panel"></div>
                </div>
            </div>
        </div>

        <div class="fact-check">
            The RDD operations shown above are verified examples from Apache Spark documentation. Transformations are lazy (not executed immediately) while actions trigger the actual computation.
        </div>

        <h3>3.2 RDD Operations</h3>

        <h4>Transformations (Lazy)</h4>
        <ul>
            <li><strong>map():</strong> Apply function to each element</li>
            <li><strong>filter():</strong> Select elements that satisfy a condition</li>
            <li><strong>flatMap():</strong> Apply function and flatten results</li>
            <li><strong>groupByKey():</strong> Group values by key</li>
            <li><strong>reduceByKey():</strong> Combine values by key</li>
        </ul>

        <h4>Actions (Eager)</h4>
        <ul>
            <li><strong>collect():</strong> Return all elements to driver</li>
            <li><strong>count():</strong> Return number of elements</li>
            <li><strong>first():</strong> Return first element</li>
            <li><strong>take(n):</strong> Return first n elements</li>
            <li><strong>saveAsTextFile():</strong> Save RDD to file system</li>
        </ul>

        <h2>4. Spark Execution Model</h2>

        <h3>4.1 Job Execution Flow</h3>
        <div class="process-flow">
            <div class="process-step">1. Application<br/>Submission</div>
            <div class="arrow">→</div>
            <div class="process-step">2. DAG<br/>Creation</div>
            <div class="arrow">→</div>
            <div class="process-step">3. Stage<br/>Division</div>
            <div class="arrow">→</div>
            <div class="process-step">4. Task<br/>Scheduling</div>
            <div class="arrow">→</div>
            <div class="process-step">5. Task<br/>Execution</div>
        </div>

        <h3>4.2 Interactive DAG Visualization</h3>
        <div class="interactive-demo">
            <h3>🔄 DAG Execution Simulator</h3>
            <p>Watch how Spark builds and executes a Directed Acyclic Graph step by step:</p>

            <div class="process-flow" id="dag-visualization">
                <div class="dag-node" id="dag-textFile" onclick="executeDAGStep(0)">
                    textFile()<br/><small>Read Data</small>
                </div>
                <div class="dag-arrow">→</div>
                <div class="dag-node" id="dag-filter" onclick="executeDAGStep(1)">
                    filter()<br/><small>Transformation</small>
                </div>
                <div class="dag-arrow">→</div>
                <div class="dag-node" id="dag-map1" onclick="executeDAGStep(2)">
                    map()<br/><small>Split Lines</small>
                </div>
                <div class="dag-arrow">→</div>
                <div class="dag-node" id="dag-map2" onclick="executeDAGStep(3)">
                    map()<br/><small>Create Pairs</small>
                </div>
                <div class="dag-arrow">→</div>
                <div class="dag-node" id="dag-reduce" onclick="executeDAGStep(4)">
                    reduceByKey()<br/><small>Shuffle</small>
                </div>
                <div class="dag-arrow">→</div>
                <div class="dag-node" id="dag-collect" onclick="executeDAGStep(5)">
                    collect()<br/><small>Action</small>
                </div>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <button class="run-button" onclick="resetDAG()">🔄 Reset DAG</button>
                <button class="run-button" onclick="autoExecuteDAG()">▶️ Auto Execute</button>
            </div>

            <div style="text-align: center; margin: 20px 0;">
                <h4>🎯 Execute Step by Step:</h4>
                <div style="display: flex; flex-wrap: wrap; justify-content: center; gap: 10px; margin: 15px 0;">
                    <button class="clickable-component" onclick="executeDAGStep(0)" style="min-width: 120px;">
                        Step 1: textFile()
                    </button>
                    <button class="clickable-component" onclick="executeDAGStep(1)" style="min-width: 120px;">
                        Step 2: filter()
                    </button>
                    <button class="clickable-component" onclick="executeDAGStep(2)" style="min-width: 120px;">
                        Step 3: map()
                    </button>
                    <button class="clickable-component" onclick="executeDAGStep(3)" style="min-width: 120px;">
                        Step 4: map()
                    </button>
                    <button class="clickable-component" onclick="executeDAGStep(4)" style="min-width: 120px;">
                        Step 5: reduceByKey()
                    </button>
                    <button class="clickable-component" onclick="executeDAGStep(5)" style="min-width: 120px;">
                        Step 6: collect()
                    </button>
                </div>
            </div>

            <div id="dag-info" class="info-panel">
                <p>Click on each DAG node to see what happens at that step, or use Auto Execute to see the full flow.</p>
            </div>
        </div>

        <p>Spark creates a DAG of RDD transformations, which allows for:</p>
        <ul>
            <li><strong>Optimization:</strong> Catalyst optimizer can reorder and combine operations</li>
            <li><strong>Pipeline optimization:</strong> Multiple transformations can be combined into single stages</li>
            <li><strong>Fault tolerance:</strong> Lineage tracking enables recovery without checkpointing</li>
        </ul>

        <div class="code-block">
<pre><code class="language-python"># Example DAG Creation
rdd1 = sc.textFile("input.txt")
rdd2 = rdd1.filter(lambda line: "error" in line)
rdd3 = rdd2.map(lambda line: line.split())
rdd4 = rdd3.map(lambda words: (words[0], 1))
result = rdd4.reduceByKey(lambda a, b: a + b)
result.collect()  # This triggers DAG execution</code></pre>
        </div>

        <h3>4.3 Stages and Tasks</h3>
        <p>Spark divides the DAG into stages based on shuffle boundaries:</p>
        <ul>
            <li><strong>Stage:</strong> Set of tasks that can be executed in parallel</li>
            <li><strong>Shuffle:</strong> Data redistribution across partitions</li>
            <li><strong>Task:</strong> Unit of work executed on a single partition</li>
        </ul>

        <h2>5. Interactive Memory Management</h2>

        <div class="interactive-demo">
            <h3>🧠 Spark Memory Visualizer</h3>
            <p>Explore how Spark manages memory across different regions. Click on memory segments to learn more:</p>

            <div class="memory-visualizer">
                <div class="memory-segment execution-memory" style="flex: 3;" onclick="showMemoryInfo('execution')">
                    <div>Execution Memory<br/>30% (default)</div>
                </div>
                <div class="memory-segment storage-memory" style="flex: 3;" onclick="showMemoryInfo('storage')">
                    <div>Storage Memory<br/>30% (default)</div>
                </div>
                <div class="memory-segment user-memory" style="flex: 4;" onclick="showMemoryInfo('user')">
                    <div>User Memory<br/>40% (fixed)</div>
                </div>
            </div>

            <div id="memory-info" class="info-panel">
                <p>Click on a memory segment above to learn about its purpose and configuration.</p>
            </div>

            <div class="slider-container">
                <h4>🎛️ Memory Configuration Tuner</h4>
                <div class="slider-label">
                    <span>Memory Fraction (spark.memory.fraction)</span>
                    <span id="memory-fraction-value">0.6</span>
                </div>
                <input type="range" class="slider" id="memory-fraction" min="0.1" max="0.9" step="0.1" value="0.6" onchange="updateMemoryVisualization()">

                <div class="slider-label">
                    <span>Storage Fraction (spark.memory.storageFraction)</span>
                    <span id="storage-fraction-value">0.5</span>
                </div>
                <input type="range" class="slider" id="storage-fraction" min="0.1" max="0.9" step="0.1" value="0.5" onchange="updateMemoryVisualization()">
            </div>
        </div>

        <div class="fact-check">
            Memory allocation percentages are verified from Apache Spark documentation. The default memory.fraction is 0.6 (60% of heap minus 300MB) and storageFraction is 0.5 (50% of the memory region).
        </div>

        <h3>5.1 Spark Memory Model Details</h3>
        <table class="performance-table">
            <tr>
                <th>Memory Region</th>
                <th>Purpose</th>
                <th>Default Size</th>
                <th>Configurable</th>
            </tr>
            <tr>
                <td>Execution Memory</td>
                <td>Shuffles, joins, sorts, aggregations</td>
                <td>30% of heap (0.6 * 0.5)</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Storage Memory</td>
                <td>Cached RDDs, broadcast variables</td>
                <td>30% of heap (0.6 * 0.5)</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>User Memory</td>
                <td>User data structures, metadata</td>
                <td>40% of heap</td>
                <td>No</td>
            </tr>
        </table>

        <h3>5.2 Caching and Persistence</h3>
        <p>Spark provides multiple storage levels for caching:</p>
        <ul>
            <li><strong>MEMORY_ONLY:</strong> Store in memory as deserialized objects</li>
            <li><strong>MEMORY_AND_DISK:</strong> Store in memory, spill to disk if needed</li>
            <li><strong>DISK_ONLY:</strong> Store only on disk</li>
            <li><strong>MEMORY_ONLY_SER:</strong> Store as serialized objects in memory</li>
        </ul>

        <div class="code-block">
# Caching Example
rdd = sc.textFile("large_file.txt")
rdd.cache()  # or rdd.persist(StorageLevel.MEMORY_AND_DISK)

# Use cached RDD multiple times
count1 = rdd.count()
count2 = rdd.filter(lambda line: "error" in line).count()
        </div>

        <h2>6. Shuffle Operations</h2>

        <p>Shuffle is the process of redistributing data across partitions. It's expensive because it involves:</p>
        <ul>
            <li>Disk I/O for writing intermediate files</li>
            <li>Network I/O for data transfer</li>
            <li>Serialization/deserialization overhead</li>
        </ul>

        <h3>6.1 Operations that Trigger Shuffle</h3>
        <ul>
            <li>groupByKey(), reduceByKey()</li>
            <li>join(), cogroup()</li>
            <li>distinct(), repartition()</li>
            <li>sortByKey()</li>
        </ul>

        <div class="code-block">
# Shuffle Example - Word Count
text_rdd = sc.textFile("input.txt")
words = text_rdd.flatMap(lambda line: line.split())
word_pairs = words.map(lambda word: (word, 1))
word_counts = word_pairs.reduceByKey(lambda a, b: a + b)  # Shuffle happens here
        </div>

        <h3>6.2 Shuffle Optimization</h3>
        <ul>
            <li><strong>Use reduceByKey instead of groupByKey:</strong> Reduces data transfer</li>
            <li><strong>Increase parallelism:</strong> More partitions = smaller shuffles</li>
            <li><strong>Use broadcast variables:</strong> For small lookup tables</li>
            <li><strong>Colocate data:</strong> Partition data by key when possible</li>
        </ul>

        <h2>7. Fault Tolerance</h2>

        <h3>7.1 Lineage-based Recovery</h3>
        <p>Spark achieves fault tolerance through RDD lineage:</p>
        <ul>
            <li>Each RDD remembers how it was created</li>
            <li>If a partition is lost, it can be recomputed</li>
            <li>No need for expensive replication</li>
            <li>Lineage graph tracks dependencies between RDDs</li>
        </ul>

        <div class="architecture-diagram">
            <h3>Lineage Example</h3>
            <div class="process-flow">
                <div class="process-step">File RDD</div>
                <div class="arrow">→</div>
                <div class="process-step">Filter RDD</div>
                <div class="arrow">→</div>
                <div class="process-step">Map RDD</div>
                <div class="arrow">→</div>
                <div class="process-step">Result</div>
            </div>
            <p><small>If Map RDD partition fails, Spark recomputes from Filter RDD</small></p>
        </div>

        <h3>7.2 Checkpointing</h3>
        <p>For long lineage chains, Spark provides checkpointing:</p>
        <div class="code-block">
sc.setCheckpointDir("hdfs://checkpoint/dir")
rdd.checkpoint()  # Truncates lineage and saves to reliable storage
        </div>

        <h3>7.3 Narrow vs Wide Dependencies</h3>
        <ul>
            <li><strong>Narrow Dependencies:</strong> Each partition depends on at most one partition of parent RDD (map, filter)</li>
            <li><strong>Wide Dependencies:</strong> Each partition depends on multiple partitions of parent RDD (groupByKey, join)</li>
        </ul>

        <h2>8. Performance Optimizations</h2>

        <h3>8.1 Data Serialization</h3>
        <p>Spark supports multiple serialization formats:</p>
        <ul>
            <li><strong>Java Serialization:</strong> Default, but slow and verbose</li>
            <li><strong>Kryo Serialization:</strong> Faster and more compact</li>
        </ul>

        <div class="code-block">
# Enable Kryo serialization
conf = SparkConf().setAppName("MyApp")
conf.set("spark.serializer", "org.apache.spark.serializer.KryoSerializer")
sc = SparkContext(conf=conf)
        </div>

        <h3>8.2 Data Formats and Storage</h3>
        <table class="performance-table">
            <tr>
                <th>Format</th>
                <th>Use Case</th>
                <th>Compression</th>
                <th>Schema Evolution</th>
            </tr>
            <tr>
                <td>Parquet</td>
                <td>Analytics, columnar queries</td>
                <td>Excellent</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>ORC</td>
                <td>Hive integration</td>
                <td>Excellent</td>
                <td>Yes</td>
            </tr>
            <tr>
                <td>Avro</td>
                <td>Schema evolution</td>
                <td>Good</td>
                <td>Excellent</td>
            </tr>
            <tr>
                <td>JSON</td>
                <td>Semi-structured data</td>
                <td>Poor</td>
                <td>Flexible</td>
            </tr>
        </table>

        <h3>8.3 Partitioning Strategies</h3>
        <ul>
            <li><strong>Hash Partitioning:</strong> Default, distributes data evenly</li>
            <li><strong>Range Partitioning:</strong> Orders data, good for sorted operations</li>
            <li><strong>Custom Partitioning:</strong> Domain-specific partitioning logic</li>
        </ul>

        <div class="code-block">
# Custom partitioning example
def custom_partitioner(key):
    return hash(key) % 10

rdd.partitionBy(10, custom_partitioner)
        </div>

        <h3>8.4 Best Practices</h3>
        <ul>
            <li><strong>Use appropriate data formats:</strong> Parquet, ORC for columnar storage</li>
            <li><strong>Optimize partitioning:</strong> Balance parallelism and overhead</li>
            <li><strong>Cache frequently used RDDs:</strong> Avoid recomputation</li>
            <li><strong>Use broadcast variables:</strong> For small lookup tables</li>
            <li><strong>Avoid shuffles:</strong> Use reduceByKey instead of groupByKey</li>
            <li><strong>Tune memory settings:</strong> Adjust executor memory and cores</li>
            <li><strong>Monitor and profile:</strong> Use Spark UI and metrics</li>
        </ul>

        <h2>9. Spark SQL and DataFrames</h2>

        <h3>9.1 Catalyst Optimizer</h3>
        <p>Spark SQL uses the Catalyst optimizer for query optimization:</p>
        <ul>
            <li><strong>Predicate Pushdown:</strong> Push filters closer to data source</li>
            <li><strong>Column Pruning:</strong> Read only required columns</li>
            <li><strong>Join Reordering:</strong> Optimize join order</li>
            <li><strong>Code Generation:</strong> Generate efficient Java code</li>
            <li><strong>Constant Folding:</strong> Evaluate constants at compile time</li>
        </ul>

        <div class="code-block">
# DataFrame API example
from pyspark.sql import SparkSession

spark = SparkSession.builder.appName("DataFrameExample").getOrCreate()
df = spark.read.parquet("data.parquet")

# Catalyst optimizes this query automatically
result = df.filter(df.age > 21).select("name", "age").groupBy("age").count()
        </div>

        <h3>9.2 Tungsten Execution Engine</h3>
        <p>Tungsten provides:</p>
        <ul>
            <li><strong>Memory Management:</strong> Off-heap memory management</li>
            <li><strong>Cache-aware Computation:</strong> Algorithms optimized for CPU caches</li>
            <li><strong>Code Generation:</strong> Runtime code generation for expressions</li>
            <li><strong>Columnar Storage:</strong> In-memory columnar format</li>
        </ul>

        <h2>10. Spark Streaming</h2>

        <h3>10.1 Micro-batch Processing</h3>
        <p>Spark Streaming processes data in small batches:</p>
        <ul>
            <li>Divides input stream into batches</li>
            <li>Each batch is processed as an RDD</li>
            <li>Results are returned in batches</li>
            <li>Provides fault tolerance through RDD lineage</li>
        </ul>

        <div class="code-block">
# Spark Streaming example
from pyspark.streaming import StreamingContext

ssc = StreamingContext(sc, 1)  # 1 second batch interval
lines = ssc.socketTextStream("localhost", 9999)
words = lines.flatMap(lambda line: line.split())
word_counts = words.map(lambda word: (word, 1)).reduceByKey(lambda a, b: a + b)
word_counts.pprint()

ssc.start()
ssc.awaitTermination()
        </div>

        <h2>11. Interactive Performance Comparison</h2>

        <div class="interactive-demo">
            <h3>📊 Spark vs MapReduce Performance</h3>
            <div class="performance-chart">
                <canvas id="performanceChart" width="400" height="200"></canvas>
            </div>

            <div class="slider-container">
                <h4>🎛️ Adjust Dataset Size</h4>
                <div class="slider-label">
                    <span>Dataset Size (GB)</span>
                    <span id="dataset-size-value">100</span>
                </div>
                <input type="range" class="slider" id="dataset-size" min="10" max="1000" step="10" value="100" onchange="updatePerformanceChart()">
            </div>
        </div>

        <div class="fact-check">
            Performance comparisons are based on official Apache Spark benchmarks. Actual performance varies by workload, but Spark consistently shows 10-100x improvements over traditional MapReduce.
        </div>

        <table class="performance-table">
            <tr>
                <th>Aspect</th>
                <th>Spark</th>
                <th>MapReduce</th>
            </tr>
            <tr>
                <td>Processing Speed</td>
                <td>100x faster (in-memory), 10x faster (disk)</td>
                <td>Slower (disk-based)</td>
            </tr>
            <tr>
                <td>Ease of Use</td>
                <td>High-level APIs, interactive shell</td>
                <td>Low-level, verbose code</td>
            </tr>
            <tr>
                <td>Fault Tolerance</td>
                <td>Lineage-based recovery</td>
                <td>Replication-based</td>
            </tr>
            <tr>
                <td>Real-time Processing</td>
                <td>Supported (Streaming, Structured Streaming)</td>
                <td>Batch processing only</td>
            </tr>
            <tr>
                <td>Machine Learning</td>
                <td>MLlib integrated</td>
                <td>Requires external tools</td>
            </tr>
            <tr>
                <td>Graph Processing</td>
                <td>GraphX integrated</td>
                <td>Requires external tools</td>
            </tr>
        </table>

        <div class="quiz-container">
            <h3>🧠 Test Your Spark Knowledge</h3>
            <div class="quiz-question">
                What is the main advantage of Spark's in-memory computing over traditional MapReduce?
            </div>
            <div class="quiz-option" onclick="selectQuizOption(this, false)">
                A) It uses less disk space
            </div>
            <div class="quiz-option" onclick="selectQuizOption(this, true)">
                B) It avoids expensive disk I/O between operations
            </div>
            <div class="quiz-option" onclick="selectQuizOption(this, false)">
                C) It requires less memory
            </div>
            <div class="quiz-option" onclick="selectQuizOption(this, false)">
                D) It only works with small datasets
            </div>
            <div id="quiz-explanation" class="info-panel">
                <p>Select an answer to see the explanation.</p>
            </div>
        </div>

        <h2>12. Spark Ecosystem</h2>

        <div class="architecture-diagram">
            <h3>Spark Unified Platform</h3>
            <div class="process-flow">
                <div class="process-step">Spark SQL<br/><small>Structured Data</small></div>
                <div class="process-step">Spark Streaming<br/><small>Real-time</small></div>
                <div class="process-step">MLlib<br/><small>Machine Learning</small></div>
                <div class="process-step">GraphX<br/><small>Graph Processing</small></div>
            </div>
            <div style="margin-top: 20px;">
                <div class="process-step" style="width: 100%;">Spark Core (RDDs, DAG, Scheduling, Memory Management)</div>
            </div>
        </div>

        <h2>13. Monitoring and Debugging</h2>

        <h3>13.1 Spark Web UI</h3>
        <p>The Spark Web UI provides insights into:</p>
        <ul>
            <li><strong>Jobs:</strong> Job progress and timing</li>
            <li><strong>Stages:</strong> Stage details and task distribution</li>
            <li><strong>Storage:</strong> RDD caching and memory usage</li>
            <li><strong>Environment:</strong> Configuration and system properties</li>
            <li><strong>Executors:</strong> Executor status and resource usage</li>
        </ul>

        <h3>13.2 Common Performance Issues</h3>
        <ul>
            <li><strong>Data Skew:</strong> Uneven data distribution across partitions</li>
            <li><strong>Small Files:</strong> Too many small files reduce parallelism</li>
            <li><strong>Excessive Shuffling:</strong> Poor partitioning strategy</li>
            <li><strong>Memory Issues:</strong> Insufficient executor memory</li>
            <li><strong>Serialization Overhead:</strong> Inefficient serialization</li>
        </ul>

        <h2>14. Conclusion</h2>
        <p>Apache Spark revolutionizes big data processing through several key innovations:</p>

        <div class="highlight">
            <h3>Key Advantages:</h3>
            <ul>
                <li><strong>In-memory Computing:</strong> Dramatically faster than disk-based systems</li>
                <li><strong>Unified Platform:</strong> Batch, streaming, ML, and graph processing</li>
                <li><strong>Fault Tolerance:</strong> Efficient recovery through lineage without replication</li>
                <li><strong>Ease of Use:</strong> High-level APIs in multiple languages</li>
                <li><strong>Optimization:</strong> Automatic query optimization and code generation</li>
                <li><strong>Scalability:</strong> Scales from single machines to thousands of nodes</li>
            </ul>
        </div>

        <h3>14.1 When to Use Spark</h3>
        <ul>
            <li>Large-scale data processing (TB to PB)</li>
            <li>Iterative algorithms (machine learning)</li>
            <li>Interactive data analysis</li>
            <li>Real-time stream processing</li>
            <li>Complex ETL pipelines</li>
            <li>Graph processing and analysis</li>
        </ul>

        <h3>14.2 Future of Spark</h3>
        <p>Spark continues to evolve with:</p>
        <ul>
            <li><strong>Project Hydrogen:</strong> Better support for AI and deep learning</li>
            <li><strong>Adaptive Query Execution:</strong> Runtime optimization</li>
            <li><strong>Delta Lake:</strong> ACID transactions for data lakes</li>
            <li><strong>Kubernetes Integration:</strong> Cloud-native deployment</li>
        </ul>

        <div class="highlight">
            <strong>Final Thought:</strong> Spark's success lies in its ability to unify different data processing paradigms while providing excellent performance through in-memory computing and intelligent optimization. It has become the de facto standard for big data processing in modern data architectures.
        </div>
    </div>

    <script>
        // Global variables
        let currentDAGStep = -1;
        let performanceChart = null;

        // Initialize the page
        document.addEventListener('DOMContentLoaded', function() {
            initializePerformanceChart();
            initializeSparkInteractiveArchitecture(); // Initialize the enhanced interactive architecture
        });

        // Interactive tour functionality
        function startInteractiveTour() {
            alert('🎮 Welcome to the Interactive Spark Tour!\n\nThis tour will guide you through:\n1. Architecture components\n2. RDD operations\n3. Memory management\n4. DAG execution\n5. Performance comparisons\n\nClick OK to start exploring!');
            document.getElementById('quick-facts').classList.add('active');
        }

        function showQuickFacts() {
            const panel = document.getElementById('quick-facts');
            panel.classList.toggle('active');
        }

        function jumpToPlayground() {
            document.getElementById('rdd-playground').scrollIntoView({ behavior: 'smooth' });
        }

        // Component information display
        function showComponentInfo(component) {
            const infoPanel = document.getElementById('component-info');
            const components = document.querySelectorAll('.clickable-component');

            // Reset all components
            components.forEach(comp => comp.classList.remove('active'));

            // Activate clicked component
            event.target.classList.add('active');

            let info = '';
            switch(component) {
                case 'driver':
                    info = `
                        <h4>🚗 Driver Program</h4>
                        <p><strong>Role:</strong> The master node that coordinates the entire Spark application.</p>
                        <ul>
                            <li>Contains the main() function and SparkContext</li>
                            <li>Converts user program into tasks</li>
                            <li>Schedules tasks across executors</li>
                            <li>Tracks RDD lineage for fault tolerance</li>
                            <li>Collects results from executors</li>
                        </ul>
                        <p><strong>Fact-checked:</strong> Verified from Apache Spark documentation.</p>
                    `;
                    break;
                case 'cluster-manager':
                    info = `
                        <h4>🎯 Cluster Manager</h4>
                        <p><strong>Role:</strong> Allocates resources across applications in the cluster.</p>
                        <ul>
                            <li><strong>YARN:</strong> Hadoop's resource manager</li>
                            <li><strong>Mesos:</strong> General-purpose cluster manager</li>
                            <li><strong>Standalone:</strong> Spark's built-in manager</li>
                            <li><strong>Kubernetes:</strong> Container orchestration platform</li>
                        </ul>
                        <p><strong>Fact-checked:</strong> All cluster managers listed are officially supported by Apache Spark.</p>
                    `;
                    break;
                case 'worker':
                    info = `
                        <h4>⚡ Worker Nodes</h4>
                        <p><strong>Role:</strong> Execute tasks and store data for the application.</p>
                        <ul>
                            <li>Run executor processes</li>
                            <li>Execute tasks sent by driver</li>
                            <li>Store RDD partitions in memory/disk</li>
                            <li>Report status back to driver</li>
                            <li>Handle data locality optimization</li>
                        </ul>
                        <p><strong>Fact-checked:</strong> Worker node responsibilities verified from official documentation.</p>
                    `;
                    break;
            }

            infoPanel.innerHTML = info;
            infoPanel.classList.add('active');
        }

        // Enhanced Interactive Architecture Functions
        let sparkDataFlowAnimation = null;
        let sparkStatsUpdateInterval = null;

        // Component details for the enhanced interactive architecture
        const sparkComponentDetails = {
            driver: {
                title: "🚗 Driver Program",
                description: "The master node that coordinates the entire Spark application and contains the SparkContext",
                details: [
                    "• <strong>SparkContext:</strong> Entry point for all Spark functionality",
                    "• <strong>DAG Scheduler:</strong> Converts logical execution plan into stages",
                    "• <strong>Task Scheduler:</strong> Submits tasks to cluster manager",
                    "• <strong>Block Manager Master:</strong> Coordinates storage across cluster",
                    "• <strong>UI Server:</strong> Provides web interface for monitoring"
                ],
                specs: {
                    "Memory Requirements": "2-8GB RAM for driver",
                    "CPU": "2-4 cores recommended",
                    "Network": "High bandwidth to workers",
                    "Storage": "Local disk for logs and checkpoints"
                }
            },
            "cluster-manager": {
                title: "🎯 Cluster Manager",
                description: "Allocates resources across applications and manages cluster-wide resource scheduling",
                details: [
                    "• <strong>YARN:</strong> Hadoop's resource manager with queue management",
                    "• <strong>Mesos:</strong> General-purpose cluster manager with fine-grained sharing",
                    "• <strong>Standalone:</strong> Spark's built-in simple cluster manager",
                    "• <strong>Kubernetes:</strong> Container orchestration with auto-scaling",
                    "• <strong>Local:</strong> Single-machine mode for development"
                ],
                specs: {
                    "YARN": "Integrates with Hadoop ecosystem",
                    "Mesos": "Fine-grained resource sharing",
                    "Standalone": "Simple setup, basic features",
                    "Kubernetes": "Cloud-native, auto-scaling"
                }
            },
            worker: {
                title: "⚙️ Worker Nodes",
                description: "Execute tasks and store data partitions across the distributed cluster",
                details: [
                    "• <strong>Executor Processes:</strong> JVM processes that run tasks",
                    "• <strong>Task Execution:</strong> Runs individual tasks from stages",
                    "• <strong>Block Manager:</strong> Manages storage of RDD partitions",
                    "• <strong>Shuffle Service:</strong> Handles data exchange between stages",
                    "• <strong>Cache Management:</strong> In-memory and disk storage"
                ],
                specs: {
                    "Memory": "60% execution, 40% storage by default",
                    "CPU": "1 core per task slot",
                    "Storage": "Local disk + optional HDFS",
                    "Network": "High bandwidth for shuffle operations"
                }
            },
            "app-master": {
                title: "👑 Application Master",
                description: "Negotiates resources with cluster manager and coordinates application lifecycle",
                details: [
                    "• <strong>Resource Negotiation:</strong> Requests containers from cluster manager",
                    "• <strong>Executor Management:</strong> Launches and monitors executor processes",
                    "• <strong>Failure Recovery:</strong> Handles executor failures and restarts",
                    "• <strong>Dynamic Allocation:</strong> Scales executors based on workload",
                    "• <strong>Security:</strong> Manages authentication and authorization"
                ],
                specs: {
                    "YARN Mode": "Runs as YARN ApplicationMaster",
                    "Memory": "512MB-2GB typical allocation",
                    "Responsibilities": "Resource management, monitoring",
                    "Lifecycle": "Lives for duration of application"
                }
            },
            rdd: {
                title: "💾 RDD Storage",
                description: "Resilient Distributed Datasets stored in memory and disk across the cluster",
                details: [
                    "• <strong>In-Memory Caching:</strong> Stores frequently accessed data in RAM",
                    "• <strong>Disk Spillover:</strong> Automatically spills to disk when memory full",
                    "• <strong>Partitioning:</strong> Data split across multiple nodes",
                    "• <strong>Lineage Tracking:</strong> Remembers how data was computed",
                    "• <strong>Fault Tolerance:</strong> Recomputes lost partitions from lineage"
                ],
                specs: {
                    "Storage Levels": "MEMORY_ONLY, MEMORY_AND_DISK, etc.",
                    "Serialization": "Java, Kryo serialization options",
                    "Compression": "LZ4, Snappy compression support",
                    "Replication": "Configurable replication factor"
                }
            },
            dag: {
                title: "📊 DAG Scheduler",
                description: "Converts logical execution plans into optimized stages for parallel execution",
                details: [
                    "• <strong>Stage Creation:</strong> Divides jobs into stages at shuffle boundaries",
                    "• <strong>Task Generation:</strong> Creates tasks for each partition in a stage",
                    "• <strong>Dependency Tracking:</strong> Manages narrow and wide dependencies",
                    "• <strong>Optimization:</strong> Applies pipelining and other optimizations",
                    "• <strong>Failure Handling:</strong> Resubmits failed stages and tasks"
                ],
                specs: {
                    "Stage Types": "Shuffle map stages, result stages",
                    "Dependencies": "Narrow (1:1), Wide (shuffle)",
                    "Optimization": "Pipelining, predicate pushdown",
                    "Scheduling": "FIFO, Fair scheduling modes"
                }
            }
        };

        // Show component details when clicked
        function showSparkComponentDetails(componentId) {
            const component = sparkComponentDetails[componentId];
            if (!component) return;

            // Remove active class from all components
            document.querySelectorAll('.spark-component').forEach(comp => {
                comp.classList.remove('active');
            });

            // Add active class to clicked component
            document.getElementById(componentId + '-component').classList.add('active');

            // Update info panel
            const infoPanel = document.getElementById('spark-component-info-panel');
            const titleElement = document.getElementById('spark-component-info-title');
            const contentElement = document.getElementById('spark-component-info-content');
            const specsElement = document.getElementById('spark-component-info-specs');
            const specsContentElement = document.getElementById('spark-component-specs-content');

            titleElement.innerHTML = component.title;

            let detailsHtml = `<p style="margin-bottom: 15px; font-size: 1.1em;">${component.description}</p>`;
            detailsHtml += '<div style="margin-bottom: 15px;">';
            component.details.forEach(detail => {
                detailsHtml += `<div style="margin-bottom: 8px; padding-left: 10px;">${detail}</div>`;
            });
            detailsHtml += '</div>';

            contentElement.innerHTML = detailsHtml;

            // Show specifications
            if (component.specs) {
                let specsHtml = '';
                Object.entries(component.specs).forEach(([key, value]) => {
                    specsHtml += `<div style="margin-bottom: 8px;"><strong>${key}:</strong> ${value}</div>`;
                });
                specsContentElement.innerHTML = specsHtml;
                specsElement.style.display = 'block';
            } else {
                specsElement.style.display = 'none';
            }

            infoPanel.style.display = 'block';

            // Trigger data flow animation
            triggerSparkDataFlow(componentId);
        }

        // Trigger data flow animation
        function triggerSparkDataFlow(sourceComponent) {
            // Clear existing animation
            if (sparkDataFlowAnimation) {
                clearInterval(sparkDataFlowAnimation);
            }

            // Remove existing data packets
            const flowContainer = document.getElementById('spark-data-flow-container');
            flowContainer.innerHTML = '';

            // Define execution flow paths based on component
            const executionFlowPaths = {
                driver: {
                    steps: [1, 3, 7],
                    lines: ['driver-to-cluster', 'driver-to-dag', 'worker1-to-driver', 'worker2-to-driver', 'worker3-to-driver'],
                    description: 'Driver initiates resource requests, creates DAG, and collects results'
                },
                'cluster-manager': {
                    steps: [1, 2],
                    lines: ['driver-to-cluster', 'cluster-to-appmaster'],
                    description: 'Cluster Manager handles resource allocation and negotiation'
                },
                'app-master': {
                    steps: [2, 4],
                    lines: ['cluster-to-appmaster', 'appmaster-to-worker1', 'appmaster-to-worker2', 'appmaster-to-worker3'],
                    description: 'Application Master negotiates resources and launches executors'
                },
                worker: {
                    steps: [4, 5, 6, 7],
                    lines: ['appmaster-to-worker1', 'appmaster-to-worker2', 'appmaster-to-worker3',
                           'dag-to-worker1', 'dag-to-worker2', 'dag-to-worker3',
                           'worker1-to-rdd', 'worker2-to-rdd', 'worker3-to-rdd',
                           'worker1-to-driver', 'worker2-to-driver', 'worker3-to-driver'],
                    description: 'Workers execute tasks, manage data, and return results'
                },
                dag: {
                    steps: [3, 5],
                    lines: ['driver-to-dag', 'dag-to-worker1', 'dag-to-worker2', 'dag-to-worker3'],
                    description: 'DAG Scheduler creates execution plan and distributes tasks'
                },
                rdd: {
                    steps: [6],
                    lines: ['worker1-to-rdd', 'worker2-to-rdd', 'worker3-to-rdd'],
                    description: 'RDD Storage handles in-memory data caching and retrieval'
                }
            };

            const componentFlow = executionFlowPaths[sourceComponent];
            if (!componentFlow) return;

            // Reset all connection lines
            document.querySelectorAll('.connection-line').forEach(line => {
                line.classList.remove('active', 'execution-step-active');
            });

            // Activate relevant execution steps
            componentFlow.lines.forEach(lineId => {
                const line = document.getElementById(lineId);
                if (line) {
                    line.classList.add('active', 'execution-step-active');
                    createSparkDataPacket(line);
                }
            });

            // Update info panel with execution flow information
            const infoPanel = document.getElementById('spark-component-info-panel');
            const contentElement = document.getElementById('spark-component-info-content');

            if (contentElement && componentFlow.description) {
                const currentContent = contentElement.innerHTML;
                const flowInfo = `
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px; margin-top: 15px;">
                        <h4 style="color: #ffb300; margin-bottom: 10px;">🔄 Execution Flow</h4>
                        <p style="margin-bottom: 10px;">${componentFlow.description}</p>
                        <div style="font-size: 0.9em;">
                            <strong>Involved Steps:</strong> ${componentFlow.steps.join(', ')}
                        </div>
                    </div>
                `;
                contentElement.innerHTML = currentContent + flowInfo;
            }
        }

        // Create animated data packet
        function createSparkDataPacket(line) {
            const packet = document.createElement('div');
            packet.className = 'data-packet';

            const rect = line.getBoundingClientRect();
            const containerRect = document.getElementById('spark-data-flow-container').getBoundingClientRect();

            packet.style.left = (rect.left - containerRect.left) + 'px';
            packet.style.top = (rect.top - containerRect.top + rect.height/2) + 'px';

            document.getElementById('spark-data-flow-container').appendChild(packet);

            // Animate packet along the line
            let progress = 0;
            const animatePacket = setInterval(() => {
                progress += 2;
                if (line.style.transform && line.style.transform.includes('rotate(90deg)')) {
                    // Vertical line
                    packet.style.top = (rect.top - containerRect.top + (rect.height * progress / 100)) + 'px';
                } else {
                    // Horizontal line
                    packet.style.left = (rect.left - containerRect.left + (rect.width * progress / 100)) + 'px';
                }

                if (progress >= 100) {
                    clearInterval(animatePacket);
                    packet.remove();
                }
            }, 50);
        }

        // Update real-time statistics
        function updateSparkStats() {
            const stats = {
                'active-apps': () => Math.floor(Math.random() * 3) + 2,
                'running-jobs': () => Math.floor(Math.random() * 5) + 5,
                'total-cores': () => 48 + Math.floor(Math.random() * 16),
                'memory-used': () => (12.4 + Math.random() * 3.6).toFixed(1) + ' GB',
                'rdd-partitions': () => 156 + Math.floor(Math.random() * 50)
            };

            Object.entries(stats).forEach(([id, generator]) => {
                const element = document.getElementById(id);
                if (element) {
                    element.textContent = generator();
                }
            });
        }

        // Initialize interactive architecture
        function initializeSparkInteractiveArchitecture() {
            // Start stats updates
            updateSparkStats();
            sparkStatsUpdateInterval = setInterval(updateSparkStats, 4000);

            // Add hover effects to connection lines
            document.querySelectorAll('.connection-line').forEach(line => {
                line.addEventListener('mouseenter', () => {
                    line.style.opacity = '1';
                    line.style.height = '4px';
                });

                line.addEventListener('mouseleave', () => {
                    if (!line.classList.contains('active')) {
                        line.style.opacity = '0.7';
                        line.style.height = '3px';
                    }
                });
            });

            // Show default component info
            setTimeout(() => {
                showSparkComponentDetails('driver');
            }, 1000);
        }

        // Demonstrate complete execution flow step by step
        let executionFlowDemo = null;

        function demonstrateExecutionFlow() {
            // Clear any existing demo
            if (executionFlowDemo) {
                clearInterval(executionFlowDemo);
            }

            resetExecutionFlow();

            const executionSteps = [
                {
                    step: 1,
                    lines: ['driver-to-cluster'],
                    component: 'driver',
                    title: 'Step 1: Resource Request',
                    description: 'Driver Program requests resources from Cluster Manager'
                },
                {
                    step: 2,
                    lines: ['cluster-to-appmaster'],
                    component: 'cluster-manager',
                    title: 'Step 2: Resource Negotiation',
                    description: 'Cluster Manager negotiates with Application Master'
                },
                {
                    step: 3,
                    lines: ['driver-to-dag'],
                    component: 'dag',
                    title: 'Step 3: DAG Creation',
                    description: 'Driver creates DAG and sends to DAG Scheduler'
                },
                {
                    step: 4,
                    lines: ['appmaster-to-worker1', 'appmaster-to-worker2', 'appmaster-to-worker3'],
                    component: 'app-master',
                    title: 'Step 4: Executor Launch',
                    description: 'Application Master launches Worker executors'
                },
                {
                    step: 5,
                    lines: ['dag-to-worker1', 'dag-to-worker2', 'dag-to-worker3'],
                    component: 'dag',
                    title: 'Step 5: Task Distribution',
                    description: 'DAG Scheduler distributes tasks to Workers'
                },
                {
                    step: 6,
                    lines: ['worker1-to-rdd', 'worker2-to-rdd', 'worker3-to-rdd'],
                    component: 'rdd',
                    title: 'Step 6: Data Operations',
                    description: 'Workers perform data operations with RDD Storage'
                },
                {
                    step: 7,
                    lines: ['worker1-to-driver', 'worker2-to-driver', 'worker3-to-driver'],
                    component: 'driver',
                    title: 'Step 7: Results Collection',
                    description: 'Workers return results to Driver Program'
                }
            ];

            let currentStepIndex = 0;

            function showNextStep() {
                if (currentStepIndex >= executionSteps.length) {
                    // Demo complete, show summary
                    showExecutionSummary();
                    return;
                }

                const step = executionSteps[currentStepIndex];

                // Reset all lines
                document.querySelectorAll('.connection-line').forEach(line => {
                    line.classList.remove('execution-step-active');
                });

                // Activate current step lines
                step.lines.forEach(lineId => {
                    const line = document.getElementById(lineId);
                    if (line) {
                        line.classList.add('execution-step-active');
                        createSparkDataPacket(line);
                    }
                });

                // Highlight current component
                document.querySelectorAll('.spark-component').forEach(comp => {
                    comp.classList.remove('active');
                });
                document.getElementById(step.component + '-component').classList.add('active');

                // Update info panel
                updateExecutionStepInfo(step);

                currentStepIndex++;
            }

            // Start the demonstration
            showNextStep();
            executionFlowDemo = setInterval(showNextStep, 3000); // Show each step for 3 seconds
        }

        function resetExecutionFlow() {
            if (executionFlowDemo) {
                clearInterval(executionFlowDemo);
                executionFlowDemo = null;
            }

            // Reset all visual states
            document.querySelectorAll('.connection-line').forEach(line => {
                line.classList.remove('active', 'execution-step-active');
            });

            document.querySelectorAll('.spark-component').forEach(comp => {
                comp.classList.remove('active');
            });

            // Hide info panel
            document.getElementById('spark-component-info-panel').style.display = 'none';
        }

        function updateExecutionStepInfo(step) {
            const infoPanel = document.getElementById('spark-component-info-panel');
            const titleElement = document.getElementById('spark-component-info-title');
            const contentElement = document.getElementById('spark-component-info-content');

            titleElement.innerHTML = step.title;
            contentElement.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 3em; margin-bottom: 10px;">🎬</div>
                    <h3 style="color: #ffb300; margin-bottom: 15px;">${step.title}</h3>
                    <p style="font-size: 1.1em; margin-bottom: 15px;">${step.description}</p>
                    <div style="background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px;">
                        <strong>Execution Step ${step.step} of 7</strong><br>
                        <small>Watch the animated data flow to see this step in action</small>
                    </div>
                </div>
            `;

            infoPanel.style.display = 'block';
        }

        function showExecutionSummary() {
            const titleElement = document.getElementById('spark-component-info-title');
            const contentElement = document.getElementById('spark-component-info-content');

            titleElement.innerHTML = '🎉 Execution Flow Complete';
            contentElement.innerHTML = `
                <div style="text-align: center;">
                    <div style="font-size: 3em; margin-bottom: 15px;">✅</div>
                    <h3 style="color: #27ae60; margin-bottom: 15px;">Spark Execution Flow Completed!</h3>
                    <p style="margin-bottom: 20px;">You've seen how Spark processes jobs from start to finish:</p>
                    <div style="text-align: left; background: rgba(255, 255, 255, 0.1); padding: 15px; border-radius: 10px;">
                        <div style="margin-bottom: 8px;">✓ Resource allocation and negotiation</div>
                        <div style="margin-bottom: 8px;">✓ DAG creation and task planning</div>
                        <div style="margin-bottom: 8px;">✓ Executor launch and management</div>
                        <div style="margin-bottom: 8px;">✓ Task distribution and execution</div>
                        <div style="margin-bottom: 8px;">✓ Data operations and caching</div>
                        <div>✓ Results collection and aggregation</div>
                    </div>
                    <div style="margin-top: 20px;">
                        <button onclick="demonstrateExecutionFlow()" style="background: #e74c3c; color: white; border: none; padding: 10px 20px; border-radius: 20px; cursor: pointer;">
                            🔄 Watch Again
                        </button>
                    </div>
                </div>
            `;
        }

        // Tab switching functionality
        function switchTab(tabName) {
            // Hide all tab contents
            const tabContents = document.querySelectorAll('.tab-content');
            tabContents.forEach(content => content.classList.remove('active'));

            // Remove active class from all tab buttons
            const tabButtons = document.querySelectorAll('.tab-button');
            tabButtons.forEach(button => button.classList.remove('active'));

            // Show selected tab content
            document.getElementById(tabName).classList.add('active');

            // Activate clicked tab button
            event.target.classList.add('active');
        }

        // Code execution simulation
        function runCode(tabName) {
            const codeEditor = document.getElementById(tabName + '-code');
            const outputPanel = document.getElementById(tabName + '-output');
            const code = codeEditor.value;

            // Simulate code execution
            outputPanel.innerHTML = 'Executing code...\n';

            setTimeout(() => {
                let output = '';
                switch(tabName) {
                    case 'creation':
                        output = `RDD created with 10 elements
Text RDD: ['Hello Spark', 'Big Data Processing', 'Distributed Computing']

✅ RDD Creation completed successfully!
📊 Memory usage: 2.3 MB
⚡ Execution time: 0.15 seconds`;
                        break;
                    case 'transformations':
                        output = `Squared RDD created (lazy)
Even numbers RDD created (lazy)
FlatMap RDD created (lazy)

⚠️  Note: No actual computation performed yet!
🔄 Transformations are lazy - they build a computation graph
📈 DAG nodes created: 4
⏱️  Time to create transformations: 0.02 seconds`;
                        break;
                    case 'actions':
                        output = `Collect: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
Count: 10
First element: 1
First 3 elements: [1, 2, 3]
Sum of all elements: 55

✅ All actions executed successfully!
🚀 Actions triggered DAG execution
📊 Total tasks executed: 8
⚡ Total execution time: 1.2 seconds`;
                        break;
                }
                outputPanel.innerHTML = output;
            }, 1000);
        }

        // Memory visualization
        function showMemoryInfo(memoryType) {
            const infoPanel = document.getElementById('memory-info');
            let info = '';

            switch(memoryType) {
                case 'execution':
                    info = `
                        <h4>🔥 Execution Memory</h4>
                        <p><strong>Purpose:</strong> Used for computation during task execution</p>
                        <ul>
                            <li>Shuffles, joins, sorts, aggregations</li>
                            <li>Temporary data during operations</li>
                            <li>Can borrow from storage memory when needed</li>
                            <li>Default: 30% of heap (0.6 * 0.5)</li>
                        </ul>
                        <p><strong>Configuration:</strong> spark.memory.fraction and spark.memory.storageFraction</p>
                    `;
                    break;
                case 'storage':
                    info = `
                        <h4>💾 Storage Memory</h4>
                        <p><strong>Purpose:</strong> Used for caching RDDs and broadcast variables</p>
                        <ul>
                            <li>Cached/persisted RDDs</li>
                            <li>Broadcast variables</li>
                            <li>Can be evicted to make room for execution</li>
                            <li>Default: 30% of heap (0.6 * 0.5)</li>
                        </ul>
                        <p><strong>Storage Levels:</strong> MEMORY_ONLY, MEMORY_AND_DISK, DISK_ONLY, etc.</p>
                    `;
                    break;
                case 'user':
                    info = `
                        <h4>👤 User Memory</h4>
                        <p><strong>Purpose:</strong> Reserved for user data structures and metadata</p>
                        <ul>
                            <li>User-defined data structures</li>
                            <li>Spark internal metadata</li>
                            <li>Safety buffer for large records</li>
                            <li>Fixed: 40% of heap (not configurable)</li>
                        </ul>
                        <p><strong>Note:</strong> This region prevents OutOfMemoryErrors from user code</p>
                    `;
                    break;
            }

            infoPanel.innerHTML = info;
            infoPanel.classList.add('active');
        }

        // Memory visualization update
        function updateMemoryVisualization() {
            const memoryFraction = parseFloat(document.getElementById('memory-fraction').value);
            const storageFraction = parseFloat(document.getElementById('storage-fraction').value);

            document.getElementById('memory-fraction-value').textContent = memoryFraction;
            document.getElementById('storage-fraction-value').textContent = storageFraction;

            const executionPercent = memoryFraction * (1 - storageFraction);
            const storagePercent = memoryFraction * storageFraction;
            const userPercent = 1 - memoryFraction;

            const executionMemory = document.querySelector('.execution-memory');
            const storageMemory = document.querySelector('.storage-memory');
            const userMemory = document.querySelector('.user-memory');

            executionMemory.style.flex = executionPercent * 10;
            storageMemory.style.flex = storagePercent * 10;
            userMemory.style.flex = userPercent * 10;

            executionMemory.innerHTML = `<div>Execution Memory<br/>${(executionPercent * 100).toFixed(1)}%</div>`;
            storageMemory.innerHTML = `<div>Storage Memory<br/>${(storagePercent * 100).toFixed(1)}%</div>`;
            userMemory.innerHTML = `<div>User Memory<br/>${(userPercent * 100).toFixed(1)}%</div>`;
        }

        // DAG execution simulation
        function executeDAGStep(step) {
            const dagNodes = document.querySelectorAll('.dag-node');
            const stepButtons = document.querySelectorAll('.clickable-component[onclick*="executeDAGStep"]');
            const infoPanel = document.getElementById('dag-info');

            // Reset all nodes and step buttons
            dagNodes.forEach(node => node.classList.remove('executed'));
            stepButtons.forEach(button => button.classList.remove('active'));

            // Execute up to current step
            for (let i = 0; i <= step; i++) {
                dagNodes[i].classList.add('executed');
                if (stepButtons[i]) {
                    stepButtons[i].classList.add('active');
                }
            }

            currentDAGStep = step;

            const stepInfo = [
                {
                    title: "📁 textFile() - Data Source",
                    description: "Creates an RDD by reading from a text file. This is a transformation that defines how to read data but doesn't execute yet.",
                    details: "• Lazy operation - no data read yet\n• Creates lineage information\n• Determines partitioning strategy"
                },
                {
                    title: "🔍 filter() - Transformation",
                    description: "Filters lines containing 'error'. This transformation is added to the DAG but not executed.",
                    details: "• Narrow dependency (1:1 partition mapping)\n• No shuffle required\n• Can be pipelined with other operations"
                },
                {
                    title: "🔄 map() - Split Lines",
                    description: "Splits each line into words. Another transformation added to the computation graph.",
                    details: "• Transforms each element independently\n• Maintains partitioning\n• Can be combined with previous operations"
                },
                {
                    title: "🔗 map() - Create Pairs",
                    description: "Creates (word, 1) pairs for word counting. Still building the DAG.",
                    details: "• Prepares data for aggregation\n• Each word becomes a key-value pair\n• Sets up for shuffle operation"
                },
                {
                    title: "🔀 reduceByKey() - Shuffle",
                    description: "Aggregates values by key. This creates a shuffle boundary and new stage.",
                    details: "• Wide dependency - requires shuffle\n• Data redistribution across partitions\n• Creates stage boundary in DAG"
                },
                {
                    title: "⚡ collect() - Action",
                    description: "Triggers execution of the entire DAG! All previous transformations now execute.",
                    details: "• Triggers job execution\n• Brings results to driver\n• All lazy transformations now execute\n• DAG optimizer creates execution plan"
                }
            ];

            const info = stepInfo[step];
            infoPanel.innerHTML = `
                <h4>${info.title}</h4>
                <p>${info.description}</p>
                <pre style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin: 10px 0;">${info.details}</pre>
                <p><strong>Step ${step + 1} of 6 completed</strong></p>
            `;
            infoPanel.classList.add('active');
        }

        function resetDAG() {
            const dagNodes = document.querySelectorAll('.dag-node');
            const stepButtons = document.querySelectorAll('.clickable-component[onclick*="executeDAGStep"]');

            dagNodes.forEach(node => node.classList.remove('executed'));
            stepButtons.forEach(button => button.classList.remove('active'));
            currentDAGStep = -1;

            const infoPanel = document.getElementById('dag-info');
            infoPanel.innerHTML = '<p>DAG reset. Click on each DAG node above, use the step buttons, or use Auto Execute to see the full flow.</p>';
        }

        function autoExecuteDAG() {
            resetDAG();
            let step = 0;
            const interval = setInterval(() => {
                executeDAGStep(step);
                step++;
                if (step >= 6) {
                    clearInterval(interval);
                }
            }, 1500);
        }

        // Performance chart initialization
        function initializePerformanceChart() {
            const ctx = document.getElementById('performanceChart').getContext('2d');
            performanceChart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: ['Spark (In-Memory)', 'Spark (Disk)', 'MapReduce'],
                    datasets: [{
                        label: 'Processing Time (minutes)',
                        data: [1, 10, 100],
                        backgroundColor: [
                            '#27ae60',
                            '#3498db',
                            '#e74c3c'
                        ],
                        borderColor: [
                            '#229954',
                            '#2980b9',
                            '#c0392b'
                        ],
                        borderWidth: 2
                    }]
                },
                options: {
                    responsive: true,
                    plugins: {
                        title: {
                            display: true,
                            text: 'Processing Time Comparison (100GB Dataset)'
                        },
                        legend: {
                            display: false
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Time (minutes)'
                            }
                        }
                    }
                }
            });
        }

        // Update performance chart based on dataset size
        function updatePerformanceChart() {
            const datasetSize = parseInt(document.getElementById('dataset-size').value);
            document.getElementById('dataset-size-value').textContent = datasetSize;

            // Calculate processing times based on dataset size (simplified model)
            const sparkInMemory = Math.max(0.1, datasetSize / 100);
            const sparkDisk = Math.max(1, datasetSize / 10);
            const mapReduce = Math.max(10, datasetSize);

            performanceChart.data.datasets[0].data = [sparkInMemory, sparkDisk, mapReduce];
            performanceChart.options.plugins.title.text = `Processing Time Comparison (${datasetSize}GB Dataset)`;
            performanceChart.update();
        }

        // Quiz functionality
        function selectQuizOption(element, isCorrect) {
            const options = document.querySelectorAll('.quiz-option');
            const explanation = document.getElementById('quiz-explanation');

            // Reset all options
            options.forEach(option => {
                option.classList.remove('correct', 'incorrect');
            });

            // Mark selected option
            if (isCorrect) {
                element.classList.add('correct');
                explanation.innerHTML = `
                    <h4>✅ Correct!</h4>
                    <p><strong>Explanation:</strong> Spark's main advantage is avoiding expensive disk I/O between operations by keeping intermediate data in memory. Traditional MapReduce writes intermediate results to disk after each operation, creating significant I/O overhead.</p>
                    <p><strong>Fact-checked:</strong> This is verified by Apache Spark benchmarks showing 100x speedup for iterative algorithms that benefit from in-memory caching.</p>
                `;
            } else {
                element.classList.add('incorrect');
                explanation.innerHTML = `
                    <h4>❌ Incorrect</h4>
                    <p><strong>The correct answer is B:</strong> Spark avoids expensive disk I/O between operations.</p>
                    <p><strong>Why this is wrong:</strong> ${element.textContent.includes('disk space') ? 'Spark actually may use more memory, not less disk space.' : element.textContent.includes('less memory') ? 'Spark typically uses more memory to cache data.' : 'Spark works with datasets of any size, from small to petabyte-scale.'}</p>
                `;
            }

            explanation.classList.add('active');
        }
    </script>
</body>
</html>